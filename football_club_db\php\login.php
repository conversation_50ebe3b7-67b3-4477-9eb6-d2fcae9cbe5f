<?php
session_start();
if(!isset($_SESSION['email'])){
require_once "connect.php"  ;  
header("Location: login.html");
exit();    
}
?>
<?php
session_start();
require_once "connect.php"  ;  
$conn = new mysqli("localhost", "root", "", "football_club");
if($conn-> connect_error){
die("Connection failed");    
}
$email= $_POST['email'];
$password= $_POST['password'];

$sql = "SELECT * FROM users WHERE email ='$email' AND password= '$password'";
$result= $conn->query($sql);
if($result-> num_rows ==1){
$_SESSION['email']=$email;
header("Location: ../index.php");

} else {
   header("Location: ../error.php?msg=" . urlencode("Invalid email or password."));
exit();
}
$conn->close();
?>

