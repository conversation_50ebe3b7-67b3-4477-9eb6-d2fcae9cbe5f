<?php
// error.php

// Get the error message from the URL query string
$error = isset($_GET['msg']) ? htmlspecialchars($_GET['msg']) : 'Unknown error occurred.';
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Error</title>
  <link rel="stylesheet" href="css/style.css" />
  <style>
    body {
      background-color: #1a1a1a;
      color: #fff;
      font-family: 'Segoe UI', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .error-box {
      background-color: #2c2c2c;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(255, 0, 0, 0.4);
      text-align: center;
      max-width: 500px;
      width: 90%;
    }
    .error-box h1 {
      color: #ff4c4c;
      margin-bottom: 20px;
    }
    .error-box p {
      font-size: 18px;
      margin-bottom: 30px;
    }
    .error-box a {
      text-decoration: none;
      background-color: #ff4c4c;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: bold;
      transition: 0.3s;
    }
    .error-box a:hover {
      background-color: #e60000;
    }
  </style>
</head>
<body>
  <div class="error-box">
    <h1>⚠ Error</h1>
    <p><?php echo $error; ?></p>
    <a href="login.html">← Back to Login</a>
  </div>
</body>
</html>