body {
  background: #121212;
  color: #fff;
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.header, .footer {
  background-color: #1e1e1e;
  padding: 20px;
  text-align: center;
}

.navbar ul {
  list-style: none;
  padding: 0;
  display: flex;
  justify-content: center;
}

.navbar ul li {
  margin: 0 15px;
}

.navbar ul li a {
  color: #fff;
  text-decoration: none;
}

.content {
  padding: 20px;
}

.login-container {
  max-width: 400px;
  margin: 100px auto;
  background: #1c1c1c;
  padding: 30px;
  border-radius: 10px;
}

input, button {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
}

.hidden {
  display: none;
}

.error-box {
  text-align: center;
  padding: 100px;
}
.dashboard-grid {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-top: 30px;
}

.card {
  background-color: #1e1e1e;
  border-radius: 10px;
  padding: 20px;
  flex: 1;
  min-width: 250px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

.card h3 {
  margin-top: 0;
  color: #00d1b2;
}

.welcome-section {
  margin-top: 30px;
}
body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #121212;
  color: #eee;
}

.header {
  background-color: #1f2937;
  padding: 20px;
  text-align: center;
  color: #00d1b2;
  box-shadow: 0 2px 10px rgba(0,0,0,0.7);
}

.navbar ul {
  list-style: none;
  padding: 0;
  margin: 15px 0 0 0;
  display: flex;
  justify-content: center;
  gap: 25px;
}

.navbar ul li a {
  text-decoration: none;
  color: #a5b4fc;
  font-weight: 600;
  transition: color 0.3s ease;
}

.navbar ul li a:hover,
.navbar ul li a.active {
  color: #00d1b2;
}

.content {
  max-width: 1100px;
  margin: 30px auto;
  padding: 0 20px;
}

h1, h2, h3 {
  color: #00d1b2;
}

.player-card, .kit-card, .stadium-card, .fan-card {
  background-color: #1e293b;
  padding: 20px;
  margin-bottom: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

table.match-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #1e293b;
  border-radius: 10px;
  overflow: hidden;
}

table.match-table th,
table.match-table td {
  padding: 15px;
  border-bottom: 1px solid #2d3748;
  text-align: left;
}

table.match-table thead {
  background-color: #334155;
}

table.match-table tbody tr:hover {
  background-color: #475569;
}

.footer {
  text-align: center;
  padding: 15px;
  background-color: #1f2937;
  color: #64748b;
  margin-top: 40px;
}