<?php
session_start();
require_once "connect.php"  ;  
if(!isset($_SESSION['email'])){
header("Location: login.html");
exit();    
}
?>
<?php
require_once "connect.php"  ;  
$conn = new mysqli("localhost", "root", "","football_club");
if($conn->connect_error){
  die("Connection failed") ;
 
}
$email= $_POST['email'];
$password= $_POST['password'];
$sql= "INSERT INTO users (email, password) VALUES ('$email', '$password')";
if($conn->query($sql)){
header("Location: ../login.html");

} else {
   header("Location: ../error.php?msg=" . urlencode("Email already registered."));
exit();
}
$conn->close();

?>