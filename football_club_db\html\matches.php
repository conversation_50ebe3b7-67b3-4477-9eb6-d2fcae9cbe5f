<?php
session_start();
if (!isset($_SESSION['email'])) {
  header("Location: login.html");
  exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Matches | Football Club</title>
  <link rel="stylesheet" href="css/style.css" />
</head>
<body>
  <header class="header">
    <h1>Match Schedule & Results</h1>
    <nav class="navbar">
      <ul>
        <li><a href="index.php">Home</a></li>
        <li><a href="players.php">Players</a></li>
        <li><a href="kits.php">Kits</a></li>
        <li><a href="stadiums.php">Stadiums</a></li>
        <li><a href="fans.php">Fans</a></li>
        <li><a href="matches.php" class="active">Matches</a></li>
        <li><a href="php/logout.php">Logout</a></li>
      </ul>
    </nav>
  </header>

  <main class="content">
    <section class="match-list">
      <table class="match-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>Opponent</th>
            <th>Venue</th>
            <th>Result</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>August 10, 2025</td>
            <td>Thunder FC</td>
            <td>Home</td>
            <td>3 - 1 Win</td>
          </tr>
          <tr>
            <td>August 24, 2025</td>
            <td>Mountain Rovers</td>
            <td>Away</td>
            <td>1 - 1 Draw</td>
          </tr>
          <tr>
            <td>September 7, 2025</td>
            <td>Coastal Sharks</td>
            <td>Home</td>
            <td>2 - 0 Win</td>
          </tr>
        </tbody>
      </table>
    </section>
  </main>

  <footer class="footer">
    &copy; 2025 Football Club. All rights reserved.
  </footer>
</body>
</html>